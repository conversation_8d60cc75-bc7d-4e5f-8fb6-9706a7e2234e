"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Alert,
  Skeleton,
  Chip,
  IconButton,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Popover,
  Button,
  Snackbar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Switch,
  FormControlLabel,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  AddCircleOutline as AddCircleOutlineIcon,
  Add as AddIcon,
  VisibilityOutlined as VisibilityOutlinedIcon,
  EditOutlined as EditOutlinedIcon,
  DeleteOutline as DeleteOutlineIcon,
  Tune as TuneIcon,
  FilterList as FilterListIcon,
} from "@mui/icons-material";
import { useAuth } from "@/lib/auth/AuthContext";
import { projectsService } from "@/lib/api/projectsService";
import { lifeAspectsService } from "@/lib/api/lifeAspectsService";
import { Project, TableRowData, ProjectHierarchy, HierarchicalTableData } from "@/lib/types/projects";
import { LifeAspect, CreateLifeAspectData } from "@/lib/types/lifeAspects";
import { formatDateProfessional } from "@/lib/utils/dateUtils";
import { outcomesService } from "@/lib/api/outcomesService";
import { transformHierarchyToTableData, getCellDisplayValue, getCellProjectData } from "@/lib/utils/hierarchyHelpers";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import PriorityEditor from "@/components/projects/PriorityEditor";
import WeekEditor from "@/components/projects/WeekEditor";
import EditProjectModal from "@/components/projects/EditProjectModal";
import CreateProjectModal from "@/components/projects/CreateProjectModal";
import CustomizeViewModal from "@/components/customizeView/CustomizeViewModal";
import ProjectFilterModal from "@/components/filters/ProjectFilterModal";
import { userPreferencesService } from "@/lib/api/userPreferencesService";
import { UserPreferences } from "@/lib/types/userPreferences";
import { ResolvedCustomField } from "@/lib/types/customFields";
import { FilterRule } from "@/lib/types/filters";

const HierarchicalOverviewPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [hierarchyData, setHierarchyData] = useState<ProjectHierarchy[]>([]);
  const [tableData, setTableData] = useState<HierarchicalTableData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for hide empty Life Aspects toggle
  const [hideEmptyLifeAspects, setHideEmptyLifeAspects] = useState(false);

  // Editor states
  const [priorityEditorOpen, setPriorityEditorOpen] = useState(false);
  const [priorityEditorAnchor, setPriorityEditorAnchor] = useState<HTMLElement | null>(null);
  const [priorityEditorProject, setPriorityEditorProject] = useState<{ id: string; priorityId?: string; priorityName?: string } | null>(null);

  const [weekEditorOpen, setWeekEditorOpen] = useState(false);
  const [weekEditorAnchor, setWeekEditorAnchor] = useState<HTMLElement | null>(null);
  const [weekEditorProject, setWeekEditorProject] = useState<{ id: string; week?: number } | null>(null);

  // State for right-click context menu
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ mouseX: number; mouseY: number } | null>(null);
  const [contextMenuProject, setContextMenuProject] = useState<{
    id: string;
    name: string;
    lifeAspectId: string;
    lifeAspectName: string;
    parentProjectId?: string;
    level: number;
  } | null>(null);

  const [editProjectDialogOpen, setEditProjectDialogOpen] = useState(false);
  const [editProjectData, setEditProjectData] = useState<{ id: string; name: string } | null>(null);

  // State for delete confirmation dialog
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<{ id: string; name: string } | null>(null);
  const [isDeletingProject, setIsDeletingProject] = useState(false);

  // State for inline editing
  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [editingProjectName, setEditingProjectName] = useState<string>("");

  // State for hover effects
  const [hoveredProjectId, setHoveredProjectId] = useState<string | null>(null);
  const [hoveredLifeAspectHeader, setHoveredLifeAspectHeader] = useState(false);

  // State for contextual project creation (modal-based)
  const [createProjectModalOpen, setCreateProjectModalOpen] = useState(false);
  const [createProjectContextData, setCreateProjectContextData] = useState<{
    type: "sub" | "sibling" | "level0";
    lifeAspectId: string;
    lifeAspectName: string;
    parentProjectId?: string;
    parentProjectName?: string;
    level: number;
  } | null>(null);

  // State for Life Aspects functionality
  const [editingLifeAspectId, setEditingLifeAspectId] = useState<string | null>(null);
  const [editingLifeAspectName, setEditingLifeAspectName] = useState<string>("");
  const [isUpdatingLifeAspect, setIsUpdatingLifeAspect] = useState(false);

  // State for Life Aspect context menu
  const [lifeAspectContextMenuOpen, setLifeAspectContextMenuOpen] = useState(false);
  const [lifeAspectContextMenuPosition, setLifeAspectContextMenuPosition] = useState<{ mouseX: number; mouseY: number } | null>(null);
  const [lifeAspectContextMenuData, setLifeAspectContextMenuData] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // State for Life Aspect creation
  const [addLifeAspectPopoverOpen, setAddLifeAspectPopoverOpen] = useState(false);
  const [addLifeAspectPopoverAnchor, setAddLifeAspectPopoverAnchor] = useState<HTMLElement | null>(null);
  const [newLifeAspectName, setNewLifeAspectName] = useState<string>("");
  const [isCreatingLifeAspect, setIsCreatingLifeAspect] = useState(false);

  // State for Life Aspect deletion
  const [deleteLifeAspectConfirmOpen, setDeleteLifeAspectConfirmOpen] = useState(false);
  const [lifeAspectToDelete, setLifeAspectToDelete] = useState<{ id: string; name: string } | null>(null);
  const [isDeletingLifeAspect, setIsDeletingLifeAspect] = useState(false);

  // State for success/error feedback
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");

  // State for Customize View functionality
  const [customizeViewModalOpen, setCustomizeViewModalOpen] = useState(false);
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  const [preferencesLoading, setPreferencesLoading] = useState(false);

  // State for Filter functionality
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterRule[]>([]);

  // Refs for auto-focus functionality
  const lifeAspectNameInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isAuthenticated) {
      fetchAllData();
      loadUserPreferences();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  // Enhanced auto-focus for Life Aspect creation popover
  useEffect(() => {
    if (addLifeAspectPopoverOpen) {
      console.log("🎯 Life Aspect popover opened, attempting auto-focus...");

      // Strategy 1: Immediate attempt
      if (lifeAspectNameInputRef.current) {
        lifeAspectNameInputRef.current.focus();
        console.log("✅ Immediate focus successful");
      }

      // Strategy 2: Short delay for DOM settling
      const timer1 = setTimeout(() => {
        if (lifeAspectNameInputRef.current) {
          lifeAspectNameInputRef.current.focus();
          lifeAspectNameInputRef.current.select();
          console.log("✅ Short delay focus successful");
        }
      }, 100);

      // Strategy 3: Longer delay for MUI Popover positioning
      const timer2 = setTimeout(() => {
        if (lifeAspectNameInputRef.current) {
          lifeAspectNameInputRef.current.focus();
          lifeAspectNameInputRef.current.select();
          console.log("✅ Long delay focus successful");
        }
      }, 300);

      // Strategy 4: Final attempt with requestAnimationFrame
      const timer3 = setTimeout(() => {
        requestAnimationFrame(() => {
          if (lifeAspectNameInputRef.current) {
            lifeAspectNameInputRef.current.focus();
            lifeAspectNameInputRef.current.select();
            console.log("✅ Animation frame focus successful");
          }
        });
      }, 500);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [addLifeAspectPopoverOpen]);

  // Helper function to ensure all Life Aspects are included in hierarchy
  const ensureAllLifeAspectsInHierarchy = (hierarchyArray: ProjectHierarchy[], allLifeAspects: LifeAspect[]): ProjectHierarchy[] => {
    const existingLifeAspectIds = new Set(hierarchyArray.map((item) => item.life_aspect?.id).filter(Boolean));

    // Add missing Life Aspects with empty projects array
    const missingLifeAspects = allLifeAspects.filter((lifeAspect) => !existingLifeAspectIds.has(lifeAspect.id));

    const missingHierarchyItems: ProjectHierarchy[] = missingLifeAspects.map((lifeAspect) => ({
      life_aspect: {
        id: lifeAspect.id,
        name: lifeAspect.name,
        description: lifeAspect.description || "",
      },
      projects: [],
    }));

    return [...hierarchyArray, ...missingHierarchyItems];
  };

  // Load user preferences
  const loadUserPreferences = async () => {
    try {
      setPreferencesLoading(true);
      const preferences = await userPreferencesService.getPreferences();
      setUserPreferences(preferences);
    } catch (error: any) {
      console.error("Error loading user preferences:", error);
      // Don't show error to user for preferences - it's not critical
    } finally {
      setPreferencesLoading(false);
    }
  };

  // Customize View handlers
  const handleOpenCustomizeView = () => {
    setCustomizeViewModalOpen(true);
  };

  const handleCloseCustomizeView = () => {
    setCustomizeViewModalOpen(false);
  };

  const handlePreferencesUpdated = (updatedPreferences: UserPreferences) => {
    setUserPreferences(updatedPreferences);
    // Show success message
    setSnackbarMessage("View preferences updated successfully!");
    setSnackbarSeverity("success");
    setSnackbarOpen(true);
  };

  // Filter handlers
  const handleOpenFilter = () => {
    setFilterModalOpen(true);
  };

  const handleCloseFilter = () => {
    setFilterModalOpen(false);
  };

  const handleFiltersApplied = (filters: FilterRule[]) => {
    setActiveFilters(filters);
    setFilterModalOpen(false);
    // Refresh data with filters
    fetchFilteredData(filters);
  };

  // Helper function to create lighter tint of a color
  const getLighterTint = (color: string, opacity: number = 0.6): string => {
    // For hex colors, we'll use CSS color-mix or rgba with opacity
    if (color.startsWith("#")) {
      // Convert hex to rgba with reduced opacity for lighter appearance
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
    // For other color formats, use CSS color-mix if supported, otherwise return original with opacity
    return color.includes("rgb") ? color.replace(")", `, ${opacity})`).replace("rgb", "rgba") : color;
  };

  // Helper functions for custom field rendering
  const getPinnedCustomField = (customFields: ResolvedCustomField[]): ResolvedCustomField | null => {
    if (!userPreferences?.preferences_data?.project_view_fields?.pinned_field_id) {
      return null;
    }

    const pinnedFieldId = userPreferences.preferences_data.project_view_fields.pinned_field_id;
    const result = customFields.find((field) => field.definition_id === pinnedFieldId) || null;

    return result;
  };

  // Helper function to get pinned static field (Start Date or End Date)
  const getPinnedStaticField = (projectData: ProjectTableData): { id: string; name: string; value: string } | null => {
    const pinnedFieldId = userPreferences?.preferences_data?.project_view_fields?.pinned_field_id;
    if (!pinnedFieldId) return null;

    // Add debug logging
    console.log("getPinnedStaticField called with:", {
      pinnedFieldId,
      projectStartDate: projectData.start_date,
      projectEndDate: projectData.end_date,
    });

    if (pinnedFieldId === "start_date" && projectData.start_date) {
      const formattedDate = formatDateProfessional(projectData.start_date);
      console.log("Formatting start_date:", projectData.start_date, "to:", formattedDate);
      return {
        id: "start_date",
        name: "Start Date",
        value: formattedDate,
      };
    }

    if (pinnedFieldId === "end_date" && projectData.end_date) {
      const formattedDate = formatDateProfessional(projectData.end_date);
      console.log("Formatting end_date:", projectData.end_date, "to:", formattedDate);
      return {
        id: "end_date",
        name: "End Date",
        value: formattedDate,
      };
    }

    return null;
  };

  const getVisibleCustomFields = (customFields: ResolvedCustomField[]): ResolvedCustomField[] => {
    const hiddenFieldIds = userPreferences?.preferences_data?.project_view_fields?.hidden_field_ids || [];
    const pinnedFieldId = userPreferences?.preferences_data?.project_view_fields?.pinned_field_id;

    const result = customFields.filter((field) => {
      // Exclude hidden fields
      if (hiddenFieldIds.includes(field.definition_id)) {
        return false;
      }
      // Exclude pinned field from regular display (it's shown inline)
      if (field.definition_id === pinnedFieldId) {
        return false;
      }
      return true;
    });

    return result;
  };

  // Helper function to check if static fields should be visible
  const shouldShowStaticField = (fieldId: string): boolean => {
    const hiddenFieldIds = userPreferences?.preferences_data?.project_view_fields?.hidden_field_ids || [];
    const pinnedFieldId = userPreferences?.preferences_data?.project_view_fields?.pinned_field_id;

    // Don't show if hidden
    if (hiddenFieldIds.includes(fieldId)) {
      return false;
    }

    // Don't show if pinned (it's shown inline)
    if (fieldId === pinnedFieldId) {
      return false;
    }

    return true;
  };

  // Enhanced badge function specifically for "Status" fields with better visibility
  const renderEnhancedCustomFieldBadge = (field: ResolvedCustomField) => {
    if (!field.value) {
      return null;
    }

    // Check if this is a "Status" field for enhanced styling
    const isStatusField = field.definition_name?.toLowerCase().includes("status");

    // For SINGLE_SELECT fields, use fully saturated colors for maximum visibility
    if (field.field_type === "SINGLE_SELECT" && field.choice_option) {
      const saturatedColor = getFullySaturatedColor(field.choice_option.color);

      return (
        <Chip
          label={field.display_value}
          variant="outlined"
          size="small"
          sx={{
            borderColor: saturatedColor,
            color: saturatedColor,
            fontWeight: 600, // Bold weight for maximum readability
            fontSize: "0.7rem", // Consistent size for better visibility
            height: "18px", // Slimmer height for sleek appearance
            borderWidth: "1.5px", // Thick border for prominence
            "& .MuiChip-label": {
              padding: "0 6px", // Reduced vertical padding for slimmer look
              fontSize: "0.7rem",
              fontWeight: 600, // Bold label text for maximum contrast
              lineHeight: 1.1, // Tighter line height for compact appearance
            },
          }}
        />
      );
    }

    // For other field types, use enhanced neutral styling with slim profile
    return (
      <Chip
        label={field.display_value}
        variant="outlined"
        size="small"
        sx={{
          borderColor: "rgba(117, 117, 117, 0.9)", // Strong grey for excellent visibility
          color: "rgba(117, 117, 117, 1)", // Full opacity grey text for maximum contrast
          fontWeight: 600, // Bold weight for maximum readability
          fontSize: "0.7rem", // Consistent size for better visibility
          height: "18px", // Slimmer height for sleek appearance
          borderWidth: "1.5px", // Thick border for prominence
          "& .MuiChip-label": {
            padding: "0 6px", // Reduced vertical padding for slimmer look
            fontSize: "0.7rem",
            fontWeight: 600, // Bold label text for maximum contrast
            lineHeight: 1.1, // Tighter line height for compact appearance
          },
        }}
      />
    );
  };

  // Legacy function for backward compatibility
  const renderCustomFieldBadge = renderEnhancedCustomFieldBadge;

  const renderCustomFieldValue = (field: ResolvedCustomField) => {
    if (!field.value) {
      return null;
    }

    // For SINGLE_SELECT fields, use the choice option color for border and text
    if (field.field_type === "SINGLE_SELECT" && field.choice_option) {
      return (
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <Typography
            variant="body2"
            sx={{
              fontSize: "0.75rem",
              color: "text.secondary",
              lineHeight: 1.3,
              fontWeight: 500,
            }}
          >
            {field.definition_name}:
          </Typography>
          {renderEnhancedCustomFieldBadge(field)}
        </Box>
      );
    }

    // For other field types, use neutral colors from style guide
    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
        <Typography
          variant="body2"
          sx={{
            fontSize: "0.75rem",
            color: "text.secondary",
            lineHeight: 1.3,
            fontWeight: 500,
          }}
        >
          {field.definition_name}:
        </Typography>
        {renderEnhancedCustomFieldBadge(field)}
      </Box>
    );
  };

  // Function to fetch filtered data
  const fetchFilteredData = async (filters: FilterRule[]) => {
    try {
      setLoading(true);
      setError(null);

      console.log("Fetching filtered data...", filters);

      // Use the new filter endpoint if filters are applied
      const [hierarchyData, allLifeAspectsData, outcomesData] = await Promise.all([
        filters.length > 0 ? projectsService.getFilteredProjectHierarchy(filters) : projectsService.getProjectHierarchy(),
        lifeAspectsService.getLifeAspects().catch((err) => {
          console.warn("Failed to fetch all life aspects:", err);
          return [];
        }),
        outcomesService.getOutcomes().catch((err) => {
          console.warn("Failed to fetch outcomes:", err);
          return [];
        }),
      ]);

      // Handle different response formats for hierarchy
      let hierarchyArray: ProjectHierarchy[];

      if (Array.isArray(hierarchyData)) {
        hierarchyArray = hierarchyData;
      } else if (hierarchyData && typeof hierarchyData === "object" && "results" in hierarchyData) {
        // Handle paginated response
        hierarchyArray = (hierarchyData as { results: ProjectHierarchy[] }).results;
      } else if (hierarchyData && typeof hierarchyData === "object") {
        // Handle single object response - convert to array
        hierarchyArray = [hierarchyData as ProjectHierarchy];
      } else {
        // Fallback to empty array
        hierarchyArray = [];
      }

      // Ensure all Life Aspects are included in hierarchy, even if they have no projects
      const completeHierarchyArray = ensureAllLifeAspectsInHierarchy(hierarchyArray, allLifeAspectsData);

      setHierarchyData(completeHierarchyArray);

      // Transform data for table rendering with outcomes data
      const transformedData = transformHierarchyToTableData(completeHierarchyArray, outcomesData);
      setTableData(transformedData);
    } catch (err: unknown) {
      console.error("Error fetching filtered data:", err);

      // Provide more helpful error messages
      let errorMessage = "Failed to load project data";
      if (err instanceof Error) {
        if (err.message?.includes("Authentication") || err.message?.includes("401")) {
          errorMessage = "Authentication required. Please log in again.";
        } else if (err.message?.includes("Network") || err.message?.includes("timeout")) {
          errorMessage = "Network error. Please check your connection and try again.";
        } else if (err.message?.includes("500")) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("Fetching all data...");

      // Fetch all data in parallel, including all Life Aspects separately
      const [hierarchyData, allLifeAspectsData, outcomesData] = await Promise.all([
        projectsService.getProjectHierarchy(true), // Force refresh to bypass any caching
        lifeAspectsService.getLifeAspects().catch((err) => {
          console.warn("Failed to fetch all life aspects:", err);
          return [];
        }),
        outcomesService.getOutcomes().catch((err) => {
          console.warn("Failed to fetch outcomes:", err);
          return [];
        }),
      ]);

      // Handle different response formats for hierarchy
      let hierarchyArray: ProjectHierarchy[];

      if (Array.isArray(hierarchyData)) {
        hierarchyArray = hierarchyData;
      } else if (hierarchyData && typeof hierarchyData === "object" && "results" in hierarchyData) {
        // Handle paginated response
        hierarchyArray = (hierarchyData as { results: ProjectHierarchy[] }).results;
      } else if (hierarchyData && typeof hierarchyData === "object") {
        // Handle single object response - convert to array
        hierarchyArray = [hierarchyData as ProjectHierarchy];
      } else {
        // Fallback to empty array
        hierarchyArray = [];
      }

      // Ensure all Life Aspects are included in hierarchy, even if they have no projects
      const completeHierarchyArray = ensureAllLifeAspectsInHierarchy(hierarchyArray, allLifeAspectsData);

      console.log("📊 Complete hierarchy data:", completeHierarchyArray);
      console.log("📊 Number of life aspects:", completeHierarchyArray.length);
      completeHierarchyArray.forEach((lifeAspect, index) => {
        console.log(`📊 Life Aspect ${index + 1}: ${lifeAspect.life_aspect.name} - ${lifeAspect.projects?.length || 0} projects`);
        if (lifeAspect.projects && lifeAspect.projects.length > 0) {
          lifeAspect.projects.forEach((project, pIndex) => {
            console.log(`  📋 Project ${pIndex + 1}: ${project.name} (Level ${project.depth || 0})`);
          });
        }
      });

      setHierarchyData(completeHierarchyArray);

      // Transform data for table rendering with outcomes data
      const transformedData = transformHierarchyToTableData(completeHierarchyArray, outcomesData);
      console.log("🔄 Transformed table data:", transformedData);
      setTableData(transformedData);
    } catch (err: unknown) {
      console.error("Error fetching data:", err);

      // Provide more helpful error messages
      let errorMessage = "Failed to load project data";
      if (err instanceof Error) {
        if (err.message?.includes("Authentication") || err.message?.includes("401")) {
          errorMessage = "Authentication required. Please log in again.";
        } else if (err.message?.includes("Network") || err.message?.includes("timeout")) {
          errorMessage = "Network error. Please check your connection and try again.";
        } else if (err.message?.includes("500")) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Function to filter table data based on hide empty Life Aspects toggle
  const getFilteredTableData = (): HierarchicalTableData | null => {
    if (!tableData) return null;

    if (!hideEmptyLifeAspects) {
      // Show all Life Aspects (default behavior)
      return tableData;
    }

    // Filter out empty Life Aspects (those with no projects)
    const filteredRows = tableData.rows.filter((row) => {
      // Keep rows that have at least one project level with content
      return row.projectLevels.length > 0;
    });

    return {
      ...tableData,
      rows: filteredRows,
    };
  };

  // Handler for toggle change
  const handleHideEmptyLifeAspectsToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setHideEmptyLifeAspects(event.target.checked);
  };

  // Event handlers for interactive elements
  const handlePriorityClick = (event: React.MouseEvent<HTMLElement>, projectData: any) => {
    setPriorityEditorAnchor(event.currentTarget);
    setPriorityEditorProject({
      id: projectData.projectId,
      priorityId: projectData.priorityLevel,
      priorityName: projectData.priorityLevelName,
    });
    setPriorityEditorOpen(true);
  };

  const handleWeekClick = (event: React.MouseEvent<HTMLElement>, projectData: any) => {
    setWeekEditorAnchor(event.currentTarget);
    setWeekEditorProject({
      id: projectData.projectId,
      week: projectData.plannedWeek,
    });
    setWeekEditorOpen(true);
  };

  // Right-click context menu handlers
  const handleContextMenu = (event: React.MouseEvent<HTMLElement>, projectData: any, row: any) => {
    event.preventDefault();
    setContextMenuPosition({
      mouseX: event.clientX + 2,
      mouseY: event.clientY - 6,
    });
    setContextMenuProject({
      id: projectData.projectId,
      name: projectData.name,
      lifeAspectId: row.lifeAspectId,
      lifeAspectName: row.lifeAspectName,
      parentProjectId: projectData.parentProjectId,
      level: projectData.level,
    });
    setContextMenuOpen(true);
  };

  const handleCloseContextMenu = () => {
    setContextMenuOpen(false);
    setContextMenuPosition(null);
    setContextMenuProject(null);
  };

  // Context menu action handlers
  const handleViewOutcomes = () => {
    if (contextMenuProject) {
      console.log("View Outcomes for project:", contextMenuProject.id, contextMenuProject.name);
      // TODO: Implement view outcomes functionality
    }
    handleCloseContextMenu();
  };

  const handleContextMenuAddSubProject = () => {
    if (contextMenuProject) {
      // Trigger the same functionality as the right-edge "+" icon
      const mockEvent = { currentTarget: null, stopPropagation: () => {} } as any;
      handleAddSubProject(mockEvent, contextMenuProject, contextMenuProject.lifeAspectId, contextMenuProject.lifeAspectName);
    }
    handleCloseContextMenu();
  };

  const handleContextMenuAddSiblingProject = () => {
    if (contextMenuProject) {
      // Trigger the same functionality as the bottom-edge "+" icon
      const mockEvent = { currentTarget: null, stopPropagation: () => {} } as any;
      handleAddSiblingProject(
        mockEvent,
        contextMenuProject,
        contextMenuProject.lifeAspectId,
        contextMenuProject.lifeAspectName,
        contextMenuProject.parentProjectId
      );
    }
    handleCloseContextMenu();
  };

  const handleEditFullDetails = () => {
    if (contextMenuProject) {
      setEditProjectData({
        id: contextMenuProject.id,
        name: contextMenuProject.name,
      });
      setEditProjectDialogOpen(true);
    }
    handleCloseContextMenu();
  };

  const handleDeleteProject = () => {
    if (contextMenuProject) {
      setProjectToDelete({
        id: contextMenuProject.id,
        name: contextMenuProject.name,
      });
      setDeleteConfirmOpen(true);
    }
    handleCloseContextMenu();
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) return;

    setIsDeletingProject(true);
    try {
      await projectsService.deleteProject(projectToDelete.id);

      // Show success message
      setSnackbarMessage(`Project "${projectToDelete.name}" deleted successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Refresh data to reflect changes
      await fetchAllData();
    } catch (error: any) {
      console.error("Failed to delete project:", error);
      setSnackbarMessage(`Failed to delete project: ${error.message || "Unknown error"}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsDeletingProject(false);
      setDeleteConfirmOpen(false);
      setProjectToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setProjectToDelete(null);
  };

  const handlePriorityChanged = (newPriorityId: string, newPriorityName: string) => {
    // Refresh data to reflect changes
    fetchAllData();
  };

  const handleWeekChanged = (newWeek: number | undefined) => {
    // Refresh data to reflect changes
    fetchAllData();
  };

  // Inline editing handlers
  const handleProjectNameClick = (projectData: any) => {
    setEditingProjectId(projectData.projectId);
    setEditingProjectName(projectData.name);
  };

  const handleProjectNameSave = async () => {
    if (!editingProjectId || !editingProjectName.trim()) {
      setEditingProjectId(null);
      setEditingProjectName("");
      return;
    }

    try {
      await projectsService.updateProject(editingProjectId, { name: editingProjectName.trim() });
      // Refresh data to reflect changes
      fetchAllData();
    } catch (error) {
      console.error("Failed to update project name:", error);
      // TODO: Show error message to user
    } finally {
      setEditingProjectId(null);
      setEditingProjectName("");
    }
  };

  const handleProjectNameCancel = () => {
    setEditingProjectId(null);
    setEditingProjectName("");
  };

  const handleProjectNameKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      event.preventDefault();
      handleProjectNameSave();
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleProjectNameCancel();
    }
  };

  // Function to get dynamic text color for completion percentage
  const getCompletionTextColor = (percentage: number): string => {
    if (percentage === 0) return "text.secondary"; // Neutral/grey for 0%
    if (percentage === 100) return "success.main"; // Green for 100%
    return "info.main"; // Blue for 1-99% (in-progress)
  };

  // Helper function to create fully saturated, high-contrast colors for maximum visibility
  const getFullySaturatedColor = (color: string): string => {
    // For hex colors, create fully saturated versions with maximum contrast
    if (color.startsWith("#")) {
      // Convert hex to rgb and create fully saturated version
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);

      // Create fully saturated version by boosting the dominant color channel
      const max = Math.max(r, g, b);
      const enhancedR = r === max ? Math.min(255, r * 1.5) : Math.max(r * 0.8, 60);
      const enhancedG = g === max ? Math.min(255, g * 1.5) : Math.max(g * 0.8, 60);
      const enhancedB = b === max ? Math.min(255, b * 1.5) : Math.max(b * 0.8, 60);

      return `rgb(${Math.round(enhancedR)}, ${Math.round(enhancedG)}, ${Math.round(enhancedB)})`;
    }
    // For other color formats, return original
    return color;
  };

  // Handlers for contextual project creation
  const handleAddSubProject = (event: React.MouseEvent<HTMLElement>, projectData: any, lifeAspectId: string, lifeAspectName: string) => {
    event.stopPropagation();
    setCreateProjectContextData({
      type: "sub",
      lifeAspectId: lifeAspectId,
      lifeAspectName: lifeAspectName,
      parentProjectId: projectData.projectId,
      parentProjectName: projectData.name,
      level: projectData.level + 1,
    });
    setCreateProjectModalOpen(true);
  };

  const handleAddSiblingProject = (
    event: React.MouseEvent<HTMLElement>,
    projectData: any,
    lifeAspectId: string,
    lifeAspectName: string,
    parentProjectId?: string
  ) => {
    event.stopPropagation();
    setCreateProjectContextData({
      type: "sibling",
      lifeAspectId: lifeAspectId,
      lifeAspectName: lifeAspectName,
      parentProjectId: parentProjectId,
      parentProjectName: projectData.name,
      level: projectData.level,
    });
    setCreateProjectModalOpen(true);
  };

  // Modal handlers for project creation
  const handleCloseCreateProjectModal = () => {
    setCreateProjectModalOpen(false);
    setCreateProjectContextData(null);
  };

  const handleCreateProjectSuccess = async (createdProject?: any) => {
    console.log("🎉 handleCreateProjectSuccess called - starting data refresh...");
    console.log("📋 Created project data:", createdProject);
    setCreateProjectModalOpen(false);
    setCreateProjectContextData(null);

    // Show success message
    setSnackbarMessage("Project created successfully!");
    setSnackbarSeverity("success");
    setSnackbarOpen(true);

    // Add a small delay to ensure backend has processed the creation
    console.log("⏳ Adding small delay to ensure backend processing...");
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Refresh data to show new project
    console.log("🔄 Calling fetchAllData to refresh project list...");
    await fetchAllData();
    console.log("✅ Data refresh completed");
  };

  // Life Aspect event handlers
  const handleLifeAspectNameClick = (lifeAspectId: string, lifeAspectName: string) => {
    setEditingLifeAspectId(lifeAspectId);
    setEditingLifeAspectName(lifeAspectName);
  };

  const handleLifeAspectNameSave = async () => {
    if (!editingLifeAspectId || !editingLifeAspectName.trim()) {
      setEditingLifeAspectId(null);
      setEditingLifeAspectName("");
      return;
    }

    setIsUpdatingLifeAspect(true);
    try {
      await lifeAspectsService.updateLifeAspect(editingLifeAspectId, {
        name: editingLifeAspectName.trim(),
      });

      // Show success message
      setSnackbarMessage(`Life Aspect updated successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Refresh data to reflect changes
      await fetchAllData();
    } catch (error: any) {
      console.error("Failed to update Life Aspect name:", error);
      setSnackbarMessage(`Failed to update Life Aspect: ${error.message || "Unknown error"}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsUpdatingLifeAspect(false);
      setEditingLifeAspectId(null);
      setEditingLifeAspectName("");
    }
  };

  const handleLifeAspectNameCancel = () => {
    setEditingLifeAspectId(null);
    setEditingLifeAspectName("");
  };

  const handleLifeAspectNameKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      event.preventDefault();
      handleLifeAspectNameSave();
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleLifeAspectNameCancel();
    }
  };

  // Life Aspect context menu handlers
  const handleLifeAspectContextMenu = (event: React.MouseEvent<HTMLElement>, lifeAspectId: string, lifeAspectName: string) => {
    event.preventDefault();
    setLifeAspectContextMenuPosition({
      mouseX: event.clientX + 2,
      mouseY: event.clientY - 6,
    });
    setLifeAspectContextMenuData({
      id: lifeAspectId,
      name: lifeAspectName,
    });
    setLifeAspectContextMenuOpen(true);
  };

  const handleCloseLifeAspectContextMenu = () => {
    setLifeAspectContextMenuOpen(false);
    setLifeAspectContextMenuPosition(null);
    setLifeAspectContextMenuData(null);
  };

  const handleAddNewLifeAspect = (anchorElement?: HTMLElement) => {
    setAddLifeAspectPopoverAnchor(anchorElement || null);
    setNewLifeAspectName("");
    setAddLifeAspectPopoverOpen(true);
    handleCloseLifeAspectContextMenu();
  };

  const handleAddLevel0Project = () => {
    if (lifeAspectContextMenuData) {
      setCreateProjectContextData({
        type: "level0",
        lifeAspectId: lifeAspectContextMenuData.id,
        lifeAspectName: lifeAspectContextMenuData.name,
        level: 0,
      });
      setCreateProjectModalOpen(true);
    }
    handleCloseLifeAspectContextMenu();
  };

  const handleDeleteLifeAspect = () => {
    if (lifeAspectContextMenuData) {
      setLifeAspectToDelete({
        id: lifeAspectContextMenuData.id,
        name: lifeAspectContextMenuData.name,
      });
      setDeleteLifeAspectConfirmOpen(true);
    }
    handleCloseLifeAspectContextMenu();
  };

  // Life Aspect creation and deletion handlers
  const handleCloseAddLifeAspectPopover = () => {
    setAddLifeAspectPopoverOpen(false);
    setAddLifeAspectPopoverAnchor(null);
    setNewLifeAspectName("");
  };

  const handleCreateLifeAspect = async () => {
    if (!newLifeAspectName.trim()) {
      return;
    }

    setIsCreatingLifeAspect(true);

    // First, let's test if we can fetch existing life aspects to verify connectivity
    console.log("🔍 Testing API connectivity...");
    try {
      const existingLifeAspects = await lifeAspectsService.getLifeAspects();
      console.log("✅ API connectivity test passed. Existing Life Aspects:", existingLifeAspects);
    } catch (connectivityError) {
      console.error("❌ API connectivity test failed:", connectivityError);
      setSnackbarMessage("Cannot connect to the backend server. Please ensure the backend is running on http://localhost:8000");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      setIsCreatingLifeAspect(false);
      return;
    }

    try {
      const createData: CreateLifeAspectData = {
        name: newLifeAspectName.trim(),
        description: "",
        color: "#1976d2", // Default primary color
        sort_order: 0,
      };

      const result = await lifeAspectsService.createLifeAspect(createData);

      // Show success message
      setSnackbarMessage(`Life Aspect "${newLifeAspectName.trim()}" created successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Close popover
      handleCloseAddLifeAspectPopover();

      // Refresh data to show new Life Aspect
      await fetchAllData();
    } catch (error: any) {
      console.error("❌ Failed to create Life Aspect:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response,
        responseData: error.response?.data,
        responseStatus: error.response?.status,
        responseHeaders: error.response?.headers,
      });

      let errorMessage = "Unknown error";

      // Enhanced error parsing
      if (error.response?.data) {
        const errorData = error.response.data;
        console.log("Error response data:", errorData);

        if (typeof errorData === "string") {
          errorMessage = errorData;
        } else if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.message) {
          errorMessage = errorData.message;
        } else if (typeof errorData === "object") {
          // Handle validation errors
          const fieldErrors = Object.keys(errorData).map((field) => {
            const fieldError = errorData[field];
            if (Array.isArray(fieldError)) {
              return `${field}: ${fieldError.join(", ")}`;
            }
            return `${field}: ${fieldError}`;
          });
          errorMessage = fieldErrors.length > 0 ? fieldErrors.join("; ") : JSON.stringify(errorData);
        }
      } else if (error.message) {
        errorMessage = error.message;
      } else if (error.details) {
        // Handle LifeAspectError format
        const details = error.details;
        const fieldErrors = Object.keys(details).map((field) => `${field}: ${details[field].join(", ")}`);
        errorMessage = fieldErrors.join("; ");
      }

      console.log("Final error message:", errorMessage);
      setSnackbarMessage(`Failed to create Life Aspect: ${errorMessage}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsCreatingLifeAspect(false);
    }
  };

  const handleNewLifeAspectKeyDown = async (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      event.preventDefault();

      // Only proceed if we have a valid life aspect name
      if (!newLifeAspectName.trim()) {
        return;
      }

      setIsCreatingLifeAspect(true);
      try {
        const createData: CreateLifeAspectData = {
          name: newLifeAspectName.trim(),
          description: "",
          color: "#1976d2",
        };

        console.log("Creating Life Aspect with data:", createData);

        const newLifeAspect = await lifeAspectsService.createLifeAspect(createData);
        console.log("✅ Life Aspect created successfully:", newLifeAspect);

        // Clear the input field and keep popover open for next entry
        setNewLifeAspectName("");

        // Refocus the input field
        setTimeout(() => {
          if (lifeAspectNameInputRef.current) {
            lifeAspectNameInputRef.current.focus();
          }
        }, 100);

        // Refresh data
        await fetchAllData();

        setSnackbarMessage(`Life Aspect "${newLifeAspect.name}" created successfully!`);
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      } catch (err: any) {
        console.error("❌ Error creating Life Aspect:", err);
        const errorMessage = err.response?.data?.detail || err.response?.data?.message || err.message || "Unknown error occurred";
        setSnackbarMessage(`Failed to create Life Aspect: ${errorMessage}`);
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      } finally {
        setIsCreatingLifeAspect(false);
      }
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleCloseAddLifeAspectPopover();
    }
  };

  const handleConfirmDeleteLifeAspect = async () => {
    if (!lifeAspectToDelete) return;

    setIsDeletingLifeAspect(true);
    try {
      await lifeAspectsService.deleteLifeAspect(lifeAspectToDelete.id);

      // Show success message
      setSnackbarMessage(`Life Aspect "${lifeAspectToDelete.name}" deleted successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Refresh data to reflect changes
      await fetchAllData();
    } catch (error: any) {
      console.error("Failed to delete Life Aspect:", error);
      setSnackbarMessage(`Failed to delete Life Aspect: ${error.message || "Unknown error"}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsDeletingLifeAspect(false);
      setDeleteLifeAspectConfirmOpen(false);
      setLifeAspectToDelete(null);
    }
  };

  const handleCancelDeleteLifeAspect = () => {
    setDeleteLifeAspectConfirmOpen(false);
    setLifeAspectToDelete(null);
  };

  const renderEnhancedTableCell = (row: any, columnIndex: number): React.ReactNode => {
    const cellData = getCellDisplayValue(row, columnIndex);
    const projectData = getCellProjectData(row, columnIndex);

    if (!cellData.shouldRender) return null;

    // Life Aspect column (enhanced with inline editing and context menu) - Sticky left column
    if (columnIndex === 0) {
      const isEditingLifeAspect = editingLifeAspectId === row.lifeAspectId;

      return (
        <TableCell
          key={`life-aspect-${row.id}`}
          rowSpan={cellData.rowSpan > 1 ? cellData.rowSpan : undefined}
          sx={{
            border: "1px solid #e0e0e0",
            verticalAlign: "top",
            padding: "6px 10px",
            fontWeight: 600, // Enhanced: Semibold for Life Aspects
            fontSize: "1rem", // Increased to make Life Aspects most prominent
            lineHeight: 1.3,
            // Sticky left positioning
            position: "sticky",
            left: 0,
            zIndex: 1, // Lower than header but above other cells
            backgroundColor: "background.paper", // Opaque background
            backgroundClip: "padding-box",
            cursor: "context-menu",
          }}
          onContextMenu={(e) => handleLifeAspectContextMenu(e, row.lifeAspectId, row.lifeAspectName)}
        >
          {isEditingLifeAspect ? (
            <TextField
              value={editingLifeAspectName}
              onChange={(e) => setEditingLifeAspectName(e.target.value)}
              onBlur={handleLifeAspectNameSave}
              onKeyDown={handleLifeAspectNameKeyDown}
              autoFocus
              variant="standard"
              size="small"
              disabled={isUpdatingLifeAspect}
              sx={{
                width: "100%",
                "& .MuiInput-input": {
                  fontWeight: 600, // Enhanced: Match Life Aspect display weight
                  fontSize: "1rem", // Match the updated display font size
                  lineHeight: 1.3,
                  padding: "2px 0",
                },
              }}
            />
          ) : (
            <Typography
              onClick={() => handleLifeAspectNameClick(row.lifeAspectId, row.lifeAspectName)}
              sx={{
                fontWeight: 600, // Enhanced: Semibold for Life Aspects
                fontSize: "1rem", // Increased to make Life Aspects most prominent
                color: "text.primary",
                lineHeight: 1.3,
                cursor: "pointer",
                width: "100%",
                "&:hover": {
                  backgroundColor: "action.hover",
                  borderRadius: "4px",
                  padding: "2px 4px",
                  margin: "-2px -4px",
                },
              }}
            >
              {cellData.value}
            </Typography>
          )}
        </TableCell>
      );
    }

    // Project columns (enhanced with priority, week, metrics, actions)
    if (projectData) {
      const isEditing = editingProjectId === projectData.projectId;
      const isHovered = hoveredProjectId === projectData.projectId;

      return (
        <TableCell
          key={`project-${projectData.projectId}-${columnIndex}`}
          rowSpan={cellData.rowSpan > 1 ? cellData.rowSpan : undefined}
          sx={{
            border: "1px solid #e0e0e0",
            verticalAlign: "top",
            padding: "6px 10px",
            minWidth: "200px",
            position: "relative",
            cursor: "context-menu",
          }}
          onMouseEnter={() => setHoveredProjectId(projectData.projectId)}
          onMouseLeave={() => setHoveredProjectId(null)}
          onContextMenu={(e) => handleContextMenu(e, projectData, row)}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 0.25, pb: 3 }}>
            {/* Project Name Row with Inline Priority */}
            <Box sx={{ display: "flex", alignItems: "center", position: "relative", flexWrap: "wrap" }}>
              {isEditing ? (
                <TextField
                  value={editingProjectName}
                  onChange={(e) => setEditingProjectName(e.target.value)}
                  onBlur={handleProjectNameSave}
                  onKeyDown={handleProjectNameKeyDown}
                  autoFocus
                  variant="standard"
                  size="small"
                  sx={{
                    flex: 1,
                    "& .MuiInput-input": {
                      fontWeight: 600, // Exact weight as requested
                      fontSize: "0.81rem", // Exact size as requested
                      lineHeight: 1.3,
                      padding: "2px 0",
                    },
                  }}
                />
              ) : (
                <>
                  <Typography
                    onClick={() => handleProjectNameClick(projectData)}
                    sx={{
                      fontWeight: 600, // Exact weight as requested
                      fontSize: "0.81rem", // Exact size as requested
                      color: "text.primary",
                      lineHeight: 1.3,
                      cursor: "pointer",
                      "&:hover": {
                        backgroundColor: "action.hover",
                        borderRadius: "4px",
                        padding: "2px 4px",
                        margin: "-2px -4px",
                      },
                    }}
                  >
                    {projectData.name}
                  </Typography>

                  {/* Pinned Field Badge (Custom or Static) */}
                  {(() => {
                    // Check for pinned custom field first
                    const pinnedCustomField = getPinnedCustomField(projectData.customFields || []);
                    if (pinnedCustomField) {
                      return (
                        <>
                          <Typography
                            component="span"
                            sx={{
                              color: "text.secondary",
                              fontSize: "0.875rem",
                              fontWeight: 400,
                              mx: 0.5,
                            }}
                          >
                            -
                          </Typography>
                          {renderCustomFieldBadge(pinnedCustomField)}
                        </>
                      );
                    }

                    // Check for pinned static field
                    const pinnedStaticField = getPinnedStaticField(projectData);
                    if (pinnedStaticField) {
                      return (
                        <>
                          <Typography
                            component="span"
                            sx={{
                              color: "text.secondary",
                              fontSize: "0.875rem",
                              fontWeight: 400,
                              mx: 0.5,
                            }}
                          >
                            -
                          </Typography>
                          <Chip
                            label={pinnedStaticField.value}
                            variant="outlined"
                            size="small"
                            sx={{
                              borderColor: "rgba(117, 117, 117, 0.9)",
                              color: "rgba(117, 117, 117, 1)",
                              fontWeight: 600,
                              fontSize: "0.7rem",
                              height: "18px",
                              borderWidth: "1.5px",
                              "& .MuiChip-label": {
                                padding: "0 6px",
                                fontSize: "0.7rem",
                                fontWeight: 600,
                                lineHeight: 1.1,
                              },
                            }}
                          />
                        </>
                      );
                    }

                    return null;
                  })()}
                </>
              )}
            </Box>

            {/* Contextual Add Project Icons - Only visible on hover */}
            {isHovered && (
              <>
                {/* Add Sub-Project Icon (Right edge) - Optimized positioning */}
                <Tooltip title="Add Sub-Project" placement="top">
                  <IconButton
                    size="small"
                    onClick={(e) => handleAddSubProject(e, projectData, row.lifeAspectId, row.lifeAspectName)}
                    sx={{
                      position: "absolute",
                      right: 4,
                      top: "50%",
                      transform: "translateY(-50%)",
                      p: 0.25,
                      minWidth: "auto",
                      width: "20px",
                      height: "20px",
                      backgroundColor: "background.paper",
                      border: "1px solid",
                      borderColor: "primary.main",
                      borderRadius: "50%",
                      boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor: "primary.light",
                        transform: "translateY(-50%) scale(1.15)",
                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.15)",
                      },
                      transition: "all 0.2s ease-in-out",
                      zIndex: 2,
                    }}
                  >
                    <AddCircleOutlineIcon sx={{ fontSize: 12, color: "primary.main" }} />
                  </IconButton>
                </Tooltip>

                {/* Edit Project Icon (Top-right corner) - Quick access */}
                <Tooltip title="Edit Project" placement="top">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditProjectData({
                        id: projectData.projectId,
                        name: projectData.name,
                      });
                      setEditProjectDialogOpen(true);
                    }}
                    sx={{
                      position: "absolute",
                      right: 28, // Position to the left of the Add Sub-Project icon
                      top: 4,
                      p: 0.25,
                      minWidth: "auto",
                      width: "20px",
                      height: "20px",
                      backgroundColor: "background.paper",
                      border: "1px solid",
                      borderColor: "info.main",
                      borderRadius: "50%",
                      boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor: "info.light",
                        transform: "scale(1.15)",
                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.15)",
                      },
                      transition: "all 0.2s ease-in-out",
                      zIndex: 2,
                    }}
                  >
                    <EditOutlinedIcon sx={{ fontSize: 12, color: "info.main" }} />
                  </IconButton>
                </Tooltip>

                {/* Add Same-Level Project Icon (Bottom edge) - Optimized positioning */}
                <Tooltip title="Add Same-Level Project" placement="bottom">
                  <IconButton
                    size="small"
                    onClick={(e) => handleAddSiblingProject(e, projectData, row.lifeAspectId, row.lifeAspectName, projectData.parentProjectId)}
                    sx={{
                      position: "absolute",
                      bottom: 4,
                      left: "50%",
                      transform: "translateX(-50%)",
                      p: 0.25,
                      minWidth: "auto",
                      width: "20px",
                      height: "20px",
                      backgroundColor: "background.paper",
                      border: "1px solid",
                      borderColor: "secondary.main",
                      borderRadius: "50%",
                      boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor: "secondary.light",
                        transform: "translateX(-50%) scale(1.15)",
                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.15)",
                      },
                      transition: "all 0.2s ease-in-out",
                      zIndex: 2,
                    }}
                  >
                    <AddIcon sx={{ fontSize: 12, color: "secondary.main" }} />
                  </IconButton>
                </Tooltip>
              </>
            )}

            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, flexWrap: "wrap" }}>
              {projectData.plannedWeek && (
                <Chip
                  label={`W${projectData.plannedWeek}`}
                  variant="outlined"
                  size="small"
                  onClick={(e) => handleWeekClick(e, projectData)}
                  sx={{
                    cursor: "pointer",
                    "&:hover": { backgroundColor: "action.hover" },
                    fontSize: "0.6875rem",
                    height: "20px",
                    "& .MuiChip-label": {
                      padding: "0 6px",
                    },
                  }}
                />
              )}
            </Box>
            {/* Custom Fields Display */}
            {(() => {
              const visibleFields = getVisibleCustomFields(projectData.customFields || []);

              if (visibleFields.length === 0) {
                return null;
              }

              return (
                <Box sx={{ display: "flex", flexDirection: "column", gap: 0.25, mt: 0.5 }}>
                  {visibleFields.map((field) => {
                    return <div key={field.definition_id}>{renderCustomFieldValue(field)}</div>;
                  })}
                </Box>
              );
            })()}

            {/* Static Date Fields Display - Respecting Visibility Preferences */}
            {(() => {
              const visibleStaticFields = [];

              // Check if Start Date should be visible
              if (projectData.startDate && shouldShowStaticField("start_date")) {
                visibleStaticFields.push({
                  id: "start_date",
                  name: "Start Date",
                  value: formatDateProfessional(projectData.startDate),
                });
              }

              // Check if End Date should be visible
              if (projectData.endDate && shouldShowStaticField("end_date")) {
                visibleStaticFields.push({
                  id: "end_date",
                  name: "End Date",
                  value: formatDateProfessional(projectData.endDate),
                });
              }

              if (visibleStaticFields.length === 0) {
                return null;
              }

              return (
                <Box sx={{ display: "flex", flexDirection: "column", gap: 0.25, mt: 0.5 }}>
                  {visibleStaticFields.map((field) => (
                    <Box key={field.id} sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: "0.75rem",
                          color: "text.secondary",
                          lineHeight: 1.3,
                          fontWeight: 500,
                        }}
                      >
                        {field.name}:
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: "0.75rem",
                          color: "text.primary",
                          lineHeight: 1.3,
                          fontWeight: 400,
                        }}
                      >
                        {field.value}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              );
            })()}

            {/* Completion Metrics - Fixed positioning on separate line */}
            <Box
              sx={{
                position: "absolute",
                bottom: 6,
                right: 10,
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                backgroundColor: "background.paper", // Ensure background for visibility
                borderRadius: "4px",
                padding: "2px 4px",
              }}
            >
              <CheckCircleIcon sx={{ fontSize: 12, color: "success.main" }} />
              <Typography
                sx={{
                  fontSize: "0.6rem", // Reduced font size as requested
                  fontWeight: 600, // Increased font weight for boldness
                  lineHeight: 1.2,
                  minWidth: "fit-content",
                }}
              >
                <Typography
                  component="span"
                  sx={{
                    color: "text.primary",
                    fontSize: "0.6rem",
                    fontWeight: 600,
                  }}
                >
                  {projectData.completedOutcomes}/{projectData.totalOutcomes} -
                </Typography>
                <Typography
                  component="span"
                  sx={{
                    color: getCompletionTextColor(projectData.completionPercentage),
                    fontSize: "0.6rem",
                    fontWeight: 600,
                  }}
                >
                  {projectData.completionPercentage}%
                </Typography>
              </Typography>
            </Box>
          </Box>
        </TableCell>
      );
    }

    return null;
  };

  const renderLoadingSkeleton = () => (
    <TableContainer component={Paper} sx={{ mt: 3 }}>
      <Table>
        <TableHead>
          <TableRow>
            {[1, 2, 3, 4].map((i) => (
              <TableCell key={i} sx={{ padding: "8px 10px" }}>
                {" "}
                {/* Compact header padding */}
                <Skeleton variant="text" width="100%" height={16} /> {/* Reduced height */}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {[1, 2, 3, 4, 5, 6, 7, 8].map(
            (
              row // More rows to show density
            ) => (
              <TableRow key={row}>
                {[1, 2, 3, 4].map((cell) => (
                  <TableCell key={cell} sx={{ padding: "6px 10px" }}>
                    {" "}
                    {/* Compact cell padding */}
                    <Skeleton variant="text" width="80%" height={14} /> {/* Reduced height */}
                  </TableCell>
                ))}
              </TableRow>
            )
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderEmptyState = () => (
    <Paper sx={{ p: 4, textAlign: "center", mt: 3 }}>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        No Projects Found
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        You haven't created any projects yet. Start by creating your first Life Aspect and Project to see them organized here.
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
        💡 <strong>New user?</strong> Life Aspects should be automatically created for you. If you don't see any, try refreshing the page or check the
        Life Aspects section.
      </Typography>
    </Paper>
  );

  if (loading) {
    return (
      <ProtectedRoute>
        <Container maxWidth="lg" sx={{ pt: 0.5, pb: 1 }}>
          <Typography variant="h6" component="h1" gutterBottom sx={{ mb: 0.5 }}>
            Project Overview
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 1 }}>
            Loading your Life Aspects and Projects hierarchy...
          </Typography>
          {renderLoadingSkeleton()}
        </Container>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      {/* Custom container for future sidebar layout - reduced left margin */}
      <Box
        sx={{
          pt: 0.5,
          pb: 1,
          pl: 2, // Reduced left padding for future sidebar space
          pr: 3, // Standard right padding
          maxWidth: "none", // Remove max-width constraint
        }}
      >
        {/* Page Header - Enhanced: Reduced visual weight and spacing */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="h6" component="h1" gutterBottom sx={{ mb: 0.5 }}>
            Project Overview
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 0 }}>
            Hierarchical view of your Life Aspects and their associated Projects organized by levels.
          </Typography>
        </Box>

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Toggle Controls */}
        {tableData && tableData.rows.length > 0 && (
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
            <Box sx={{ display: "flex", gap: 1 }}>
              {/* Filter Button */}
              <Button
                variant="outlined"
                size="small"
                startIcon={<FilterListIcon />}
                onClick={handleOpenFilter}
                sx={{
                  borderRadius: "8px",
                  textTransform: "none",
                  fontSize: "0.875rem",
                  color: activeFilters.length > 0 ? "primary.main" : "text.secondary",
                  borderColor: activeFilters.length > 0 ? "primary.main" : "divider",
                  backgroundColor: activeFilters.length > 0 ? "primary.light" : "transparent",
                  "&:hover": {
                    borderColor: "primary.main",
                    backgroundColor: "primary.light",
                    color: "primary.main",
                  },
                }}
              >
                Filter {activeFilters.length > 0 && `(${activeFilters.length})`}
              </Button>

              {/* Customize View Button */}
              <Button
                variant="outlined"
                size="small"
                startIcon={<TuneIcon />}
                onClick={handleOpenCustomizeView}
                sx={{
                  borderRadius: "8px",
                  textTransform: "none",
                  fontSize: "0.875rem",
                  color: "text.secondary",
                  borderColor: "divider",
                  "&:hover": {
                    borderColor: "primary.main",
                    backgroundColor: "primary.light",
                    color: "primary.main",
                  },
                }}
              >
                Customize View
              </Button>
            </Box>

            {/* Hide Empty Life Aspects Toggle */}
            <FormControlLabel
              control={
                <Switch
                  checked={hideEmptyLifeAspects}
                  onChange={handleHideEmptyLifeAspectsToggle}
                  size="small"
                  sx={{
                    "& .MuiSwitch-switchBase.Mui-checked": {
                      color: "primary.main",
                    },
                    "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                      backgroundColor: "primary.main",
                    },
                  }}
                />
              }
              label={
                <Typography variant="body2" sx={{ fontSize: "0.875rem", color: "text.secondary" }}>
                  Hide empty Life Aspects
                </Typography>
              }
              sx={{ mr: 0 }}
            />
          </Box>
        )}

        {/* Main Content */}
        {(() => {
          const filteredData = getFilteredTableData();
          return !filteredData || filteredData.rows.length === 0 ? (
            renderEmptyState()
          ) : (
            <TableContainer
              component={Paper}
              className="hidden-scrollbar"
              sx={{
                mt: 1,
                boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.08)",
                borderRadius: "12px",
                overflow: "auto",
                width: "80vw",
                height: "80vh",
                paddingBottom: "16px", // Gap below table content
              }}
            >
              <Table
                stickyHeader
                className="sticky-table"
                sx={{
                  borderCollapse: "separate",
                  borderSpacing: 0,
                }}
              >
                <TableHead>
                  <TableRow>
                    {filteredData.columnHeaders.map((header, index) => (
                      <TableCell
                        key={header}
                        sx={{
                          backgroundColor: "grey.100",
                          fontWeight: 500, // Reduced from 600 to 500
                          fontSize: "0.875rem", // Reduced from default to 14px
                          border: "1px solid #e0e0e0",
                          minWidth: index === 0 ? "160px" : "200px", // Reduced widths
                          color: "text.primary",
                          position: "sticky",
                          top: 0,
                          zIndex: index === 0 ? 3 : 2, // Higher z-index for first column (sticky both ways)
                          padding: "8px 10px", // Reduced padding for header
                          lineHeight: 1.3,
                          // Ensure opaque background for sticky header
                          backgroundClip: "padding-box",
                          // Add sticky left positioning for first column
                          ...(index === 0 && {
                            left: 0,
                            position: "sticky", // This will be both top and left sticky
                          }),
                        }}
                        onMouseEnter={() => index === 0 && setHoveredLifeAspectHeader(true)}
                        onMouseLeave={() => index === 0 && setHoveredLifeAspectHeader(false)}
                      >
                        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", position: "relative" }}>
                          <Typography sx={{ fontWeight: 500, fontSize: "0.875rem", lineHeight: 1.3 }}>{header}</Typography>

                          {/* Add Life Aspect Icon - Only visible on hover for first column */}
                          {index === 0 && hoveredLifeAspectHeader && (
                            <Tooltip title="Add New Life Aspect" placement="top">
                              <IconButton
                                size="small"
                                onClick={(e) => handleAddNewLifeAspect(e.currentTarget)}
                                sx={{
                                  position: "absolute",
                                  right: 4,
                                  top: "50%",
                                  transform: "translateY(-50%)",
                                  p: 0.25,
                                  minWidth: "auto",
                                  width: "20px",
                                  height: "20px",
                                  backgroundColor: "background.paper",
                                  border: "1px solid",
                                  borderColor: "primary.main",
                                  borderRadius: "50%",
                                  boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                                  "&:hover": {
                                    backgroundColor: "primary.light",
                                    transform: "translateY(-50%) scale(1.15)",
                                    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.15)",
                                  },
                                  transition: "all 0.2s ease-in-out",
                                  zIndex: 4,
                                }}
                              >
                                <AddCircleOutlineIcon sx={{ fontSize: 12, color: "primary.main" }} />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredData.rows.map((row, rowIndex) => (
                    <TableRow
                      key={row.id}
                      sx={{
                        "&:hover": {
                          backgroundColor: "grey.50",
                        },
                        "&:nth-of-type(even)": {
                          backgroundColor: "rgba(0, 0, 0, 0.02)",
                        },
                      }}
                    >
                      {filteredData.columnHeaders.map((_, columnIndex) => renderEnhancedTableCell(row, columnIndex))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          );
        })()}

        {/* Summary Information */}
        {tableData && tableData.rows.length > 0 && (
          <Box sx={{ mt: 3, display: "flex", gap: 2, flexWrap: "wrap" }}>
            <Chip label={`${Array.isArray(hierarchyData) ? hierarchyData.length : 0} Life Aspects`} variant="outlined" color="primary" />
            <Chip label={`${tableData.rows.length} Total Projects`} variant="outlined" color="secondary" />
            <Chip label={`Max Depth: ${tableData.maxDepth} levels`} variant="outlined" color="info" />
          </Box>
        )}

        {/* Interactive Components */}

        {/* Priority Editor */}
        <PriorityEditor
          projectId={priorityEditorProject?.id || ""}
          currentPriorityId={priorityEditorProject?.priorityId}
          currentPriorityName={priorityEditorProject?.priorityName}
          anchorEl={priorityEditorAnchor}
          open={priorityEditorOpen}
          onClose={() => {
            setPriorityEditorOpen(false);
            setPriorityEditorAnchor(null);
            setPriorityEditorProject(null);
          }}
          onPriorityChanged={handlePriorityChanged}
        />

        {/* Week Editor */}
        <WeekEditor
          projectId={weekEditorProject?.id || ""}
          currentWeek={weekEditorProject?.week}
          anchorEl={weekEditorAnchor}
          open={weekEditorOpen}
          onClose={() => {
            setWeekEditorOpen(false);
            setWeekEditorAnchor(null);
            setWeekEditorProject(null);
          }}
          onWeekChanged={handleWeekChanged}
        />

        {/* Right-Click Context Menu */}
        <Menu
          open={contextMenuOpen}
          onClose={handleCloseContextMenu}
          anchorReference="anchorPosition"
          anchorPosition={contextMenuPosition !== null ? { top: contextMenuPosition.mouseY, left: contextMenuPosition.mouseX } : undefined}
          PaperProps={{
            sx: {
              minWidth: 220,
              boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
              borderRadius: 2,
            },
          }}
        >
          <MenuItem onClick={handleViewOutcomes}>
            <ListItemIcon>
              <VisibilityOutlinedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="View Outcomes" />
          </MenuItem>

          <Divider />

          <MenuItem onClick={handleContextMenuAddSubProject}>
            <ListItemIcon>
              <AddCircleOutlineIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Add Sub-Project" />
          </MenuItem>

          <MenuItem onClick={handleContextMenuAddSiblingProject}>
            <ListItemIcon>
              <AddIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Add Same-Level Project" />
          </MenuItem>

          <Divider />

          <MenuItem onClick={handleEditFullDetails}>
            <ListItemIcon>
              <EditOutlinedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Edit Full Details..." />
          </MenuItem>

          <Divider />

          <MenuItem
            onClick={handleDeleteProject}
            sx={{
              color: "error.main",
              "&:hover": {
                backgroundColor: "error.light",
                color: "error.contrastText",
              },
            }}
          >
            <ListItemIcon sx={{ color: "inherit" }}>
              <DeleteOutlineIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Delete Project" />
          </MenuItem>
        </Menu>

        {/* Edit Project Modal - Comprehensive */}
        {editProjectData && (
          <EditProjectModal
            open={editProjectDialogOpen}
            projectId={editProjectData.id}
            onClose={() => {
              setEditProjectDialogOpen(false);
              setEditProjectData(null);
            }}
            onSuccess={() => {
              setEditProjectDialogOpen(false);
              setEditProjectData(null);
              fetchAllData(); // Refresh the table data
            }}
          />
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteConfirmOpen} onClose={handleCancelDelete} maxWidth="sm" fullWidth>
          <DialogTitle sx={{ color: "error.main" }}>Delete Project</DialogTitle>
          <DialogContent>
            <DialogContentText>Are you sure you want to delete the project "{projectToDelete?.name}"?</DialogContentText>
            <DialogContentText sx={{ mt: 2, fontWeight: 500, color: "error.main" }}>This action cannot be undone.</DialogContentText>
          </DialogContent>
          <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end", p: 2 }}>
            <Button onClick={handleCancelDelete} disabled={isDeletingProject}>
              Cancel
            </Button>
            <Button onClick={handleConfirmDelete} variant="contained" color="error" disabled={isDeletingProject} sx={{ minWidth: 100 }}>
              {isDeletingProject ? "Deleting..." : "Delete"}
            </Button>
          </Box>
        </Dialog>

        {/* Create Project Modal */}
        <CreateProjectModal
          open={createProjectModalOpen}
          contextData={createProjectContextData}
          onClose={handleCloseCreateProjectModal}
          onSuccess={handleCreateProjectSuccess}
        />

        {/* Life Aspect Context Menu */}
        <Menu
          open={lifeAspectContextMenuOpen}
          onClose={handleCloseLifeAspectContextMenu}
          anchorReference="anchorPosition"
          anchorPosition={
            lifeAspectContextMenuPosition !== null
              ? { top: lifeAspectContextMenuPosition.mouseY, left: lifeAspectContextMenuPosition.mouseX }
              : undefined
          }
          PaperProps={{
            sx: {
              minWidth: 220,
              boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
              borderRadius: 2,
            },
          }}
        >
          <MenuItem onClick={() => handleAddNewLifeAspect()}>
            <ListItemIcon>
              <AddCircleOutlineIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Add New Life Aspect" />
          </MenuItem>

          <MenuItem onClick={handleAddLevel0Project}>
            <ListItemIcon>
              <AddIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Add Level 0 Project" />
          </MenuItem>

          <Divider />

          <MenuItem
            onClick={handleDeleteLifeAspect}
            sx={{
              color: "error.main",
              "&:hover": {
                backgroundColor: "error.light",
                color: "error.contrastText",
              },
            }}
          >
            <ListItemIcon sx={{ color: "inherit" }}>
              <DeleteOutlineIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Delete Life Aspect" />
          </MenuItem>
        </Menu>

        {/* Add Life Aspect Popover */}
        <Popover
          open={addLifeAspectPopoverOpen}
          anchorEl={addLifeAspectPopoverAnchor}
          onClose={handleCloseAddLifeAspectPopover}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "center",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "center",
          }}
          PaperProps={{
            sx: {
              p: 2,
              minWidth: 280,
              boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
              borderRadius: 2,
            },
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Typography variant="subtitle1" fontWeight={500}>
              Add New Life Aspect
            </Typography>

            <Typography variant="body2" color="text.secondary">
              Create a new Life Aspect to organize your projects and goals.
            </Typography>

            <TextField
              label="Life Aspect Name"
              value={newLifeAspectName}
              onChange={(e) => setNewLifeAspectName(e.target.value)}
              onKeyDown={handleNewLifeAspectKeyDown}
              inputRef={lifeAspectNameInputRef}
              autoFocus
              fullWidth
              size="small"
              variant="outlined"
              disabled={isCreatingLifeAspect}
              sx={{
                "& .MuiOutlinedInput-root": {
                  fontSize: "0.875rem",
                },
              }}
            />

            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
              <Button onClick={handleCloseAddLifeAspectPopover} size="small" disabled={isCreatingLifeAspect}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateLifeAspect}
                variant="contained"
                size="small"
                disabled={!newLifeAspectName.trim() || isCreatingLifeAspect}
                sx={{
                  minWidth: 80,
                }}
              >
                {isCreatingLifeAspect ? "Creating..." : "Create"}
              </Button>
            </Box>
          </Box>
        </Popover>

        {/* Delete Life Aspect Confirmation Dialog */}
        <Dialog open={deleteLifeAspectConfirmOpen} onClose={handleCancelDeleteLifeAspect} maxWidth="sm" fullWidth>
          <DialogTitle sx={{ color: "error.main" }}>Delete Life Aspect</DialogTitle>
          <DialogContent>
            <DialogContentText>Are you sure you want to delete the Life Aspect "{lifeAspectToDelete?.name}"?</DialogContentText>
            <DialogContentText sx={{ mt: 2, fontWeight: 500, color: "error.main" }}>
              All of its projects and sub-projects will also be permanently deleted. This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelDeleteLifeAspect} disabled={isDeletingLifeAspect}>
              Cancel
            </Button>
            <Button onClick={handleConfirmDeleteLifeAspect} variant="contained" color="error" disabled={isDeletingLifeAspect} sx={{ minWidth: 100 }}>
              {isDeletingLifeAspect ? "Deleting..." : "Delete"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Customize View Modal */}
        <CustomizeViewModal open={customizeViewModalOpen} onClose={handleCloseCustomizeView} onPreferencesUpdated={handlePreferencesUpdated} />

        {/* Filter Modal */}
        <ProjectFilterModal
          open={filterModalOpen}
          onClose={handleCloseFilter}
          onFiltersApplied={handleFiltersApplied}
          initialFilters={activeFilters}
        />

        {/* Success/Error Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert onClose={() => setSnackbarOpen(false)} severity={snackbarSeverity} sx={{ width: "100%" }}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Box>
    </ProtectedRoute>
  );
};

export default HierarchicalOverviewPage;

interface ProjectTableData extends TableRowData {
  name: string;
  rowSpan: number;
  level: number;
  shouldRender: boolean;
  projectId: string;
  start_date?: string | null;
  end_date?: string | null;
  priorityLevel?: string;
  priorityLevelName?: string;
  priorityLevelColor?: string;
  parentProjectId?: string;
}
