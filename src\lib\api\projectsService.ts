import { AxiosError } from "axios";
import apiClient from "./apiClient";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { Project, CreateProjectData, UpdateProjectData, ProjectHierarchy, ProjectError } from "@/lib/types/projects";
import { FilterRule } from "@/lib/types/filters";

/**
 * Parse API errors into a consistent format
 */
const parseApiError = (error: AxiosError): ProjectError => {
  console.log("=== PARSE API ERROR DEBUG ===");
  console.log("Original error:", error);
  console.log("Error response:", error.response);
  console.log("Error response data:", error.response?.data);
  console.log("Error response status:", error.response?.status);
  console.log("Error message:", error.message);

  if (error.response?.data) {
    const data = error.response.data as any;
    console.log("Response data type:", typeof data);
    console.log("Response data keys:", Object.keys(data));

    // Handle validation errors with field details
    if (data.errors || (typeof data === "object" && !data.message && !data.detail)) {
      console.log("Handling as validation error with details");
      return {
        message: "Please check the form for errors",
        details: data.errors || data,
      };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      console.log("Handling as simple error message");
      return {
        message: data.message || data.detail || data.error,
        details: data, // Preserve original data for debugging
      };
    }

    // Handle string responses
    if (typeof data === "string") {
      console.log("Handling as string error");
      return {
        message: data,
        details: { originalData: [data] },
      };
    }
  }

  console.log("Using default error message");
  console.log("=== END PARSE API ERROR DEBUG ===");

  // Default error message
  return {
    message: error.response?.status === 401 ? "Authentication required" : "An unexpected error occurred. Please try again.",
    details: {
      status: error.response?.status,
      statusText: error.response?.statusText,
      originalMessage: error.message,
      responseData: error.response?.data,
    },
  };
};

export const projectsService = {
  /**
   * Get all projects for the authenticated user
   */
  async getProjects(): Promise<Project[]> {
    try {
      const response = await apiClient.get<Project[]>(API_ENDPOINTS.WORK_ITEMS.PROJECTS);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Get filtered project hierarchy for the authenticated user
   * This endpoint accepts filter rules and returns filtered hierarchical data
   */
  async getFilteredProjectHierarchy(filters: FilterRule[]): Promise<ProjectHierarchy[]> {
    try {
      console.log("Fetching filtered project hierarchy...", filters);
      const response = await apiClient.post<ProjectHierarchy[]>(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}/filter/`, { filters });
      return response.data;
    } catch (error) {
      console.error("Error fetching filtered project hierarchy:", error);
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Get project hierarchy for the authenticated user
   * This endpoint returns the full nested structure optimized for hierarchical display
   * Falls back to building hierarchy from separate endpoints if needed
   */
  async getProjectHierarchy(forceRefresh: boolean = false): Promise<ProjectHierarchy[]> {
    try {
      console.log("🌐 API: Fetching project hierarchy...", forceRefresh ? "(force refresh)" : "");

      // Add cache-busting parameter if force refresh is requested
      const url = forceRefresh ? `${API_ENDPOINTS.WORK_ITEMS.PROJECTS_HIERARCHY}?_t=${Date.now()}` : API_ENDPOINTS.WORK_ITEMS.PROJECTS_HIERARCHY;

      const response = await apiClient.get<ProjectHierarchy[]>(url);
      console.log("🌐 API: Hierarchy endpoint response:", response.data);
      console.log("🌐 API: Response data type:", typeof response.data);
      console.log("🌐 API: Is response array:", Array.isArray(response.data));

      // Transform the response data to match our expected format
      let hierarchyData: ProjectHierarchy[];

      if (Array.isArray(response.data)) {
        // Already in expected format
        hierarchyData = response.data;
      } else if (response.data && typeof response.data === "object") {
        // Backend returns object with life aspect names as keys
        // Transform: { "Mind": { life_aspect: {...}, root_projects: [...] } }
        // To: [{ life_aspect: {...}, projects: [...] }]
        hierarchyData = Object.values(response.data).map((item: any) => ({
          life_aspect: item.life_aspect,
          projects: item.root_projects || [],
        }));

        console.log("Transformed hierarchy data:", hierarchyData);
      } else {
        console.warn("Unexpected response format:", response.data);
        hierarchyData = [];
      }

      // Validate transformed data structure
      hierarchyData.forEach((item, index) => {
        console.log(`Hierarchy item ${index}:`, item);
        if (item && item.life_aspect) {
          console.log(`Life aspect ${index}:`, item.life_aspect);
        } else {
          console.warn(`Invalid life aspect data at index ${index}:`, item);
        }
      });

      return hierarchyData;
    } catch (error) {
      console.log("Hierarchy endpoint failed, attempting fallback approach...");
      console.error("Hierarchy endpoint error:", error);

      // Fallback: Build hierarchy from separate endpoints
      try {
        const [lifeAspectsResponse, projectsResponse] = await Promise.all([
          apiClient.get(API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS),
          apiClient.get(API_ENDPOINTS.WORK_ITEMS.PROJECTS),
        ]);

        console.log("Life aspects response:", lifeAspectsResponse.data);
        console.log("Projects response:", projectsResponse.data);

        // Build hierarchy structure from separate data
        const lifeAspects = Array.isArray(lifeAspectsResponse.data) ? lifeAspectsResponse.data : [];
        const projects = Array.isArray(projectsResponse.data) ? projectsResponse.data : [];

        // Validate life aspects data
        const validLifeAspects = lifeAspects.filter((lifeAspect: any) => {
          if (!lifeAspect || !lifeAspect.id || !lifeAspect.name) {
            console.warn("Invalid life aspect found:", lifeAspect);
            return false;
          }
          return true;
        });

        const hierarchyData: ProjectHierarchy[] = validLifeAspects.map((lifeAspect: any) => ({
          life_aspect: {
            id: lifeAspect.id,
            name: lifeAspect.name,
            description: lifeAspect.description || "",
          },
          projects: projects.filter((project: any) => project.life_aspect === lifeAspect.id),
        }));

        console.log("Built hierarchy from fallback:", hierarchyData);
        return hierarchyData;
      } catch (fallbackError) {
        console.error("Both hierarchy and fallback approaches failed:", fallbackError);
        console.error("Original hierarchy error:", error);
        console.error("Fallback error:", fallbackError);
        throw parseApiError(error as AxiosError);
      }
    }
  },

  /**
   * Get a specific project by ID
   */
  async getProject(id: string): Promise<Project> {
    try {
      const response = await apiClient.get<Project>(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}${id}/`);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Create a new project
   */
  async createProject(data: CreateProjectData): Promise<Project> {
    try {
      console.log("🌐 API: Creating project with data:", data);
      const response = await apiClient.post<Project>(API_ENDPOINTS.WORK_ITEMS.PROJECTS, data);
      console.log("🌐 API: Project creation response:", response.data);
      console.log("🌐 API: Response status:", response.status);
      return response.data;
    } catch (error) {
      console.error("🌐 API: Project creation failed:", error);
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Update an existing project
   */
  async updateProject(id: string, data: UpdateProjectData): Promise<Project> {
    try {
      const response = await apiClient.patch<Project>(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}${id}/`, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Delete a project
   */
  async deleteProject(id: string): Promise<void> {
    try {
      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}${id}/`);
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },
};
