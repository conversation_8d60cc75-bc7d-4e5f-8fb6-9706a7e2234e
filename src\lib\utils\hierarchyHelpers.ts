import { Project, ProjectHierarchy, TableRowData, HierarchicalTableData } from "@/lib/types/projects";
import { Outcome, isOutcomeCompleted } from "@/lib/types/outcomes";
import { ResolvedCustomField } from "@/lib/types/customFields";

/**
 * Normalize project data to use consistent field names
 */
function normalizeProject(project: Project): Project {
  return {
    ...project,
    children: project.children || project.sub_projects || [],
  };
}

/**
 * Calculate the total number of leaf nodes (projects without children) in a project tree
 */
function countLeafNodes(project: Project): number {
  const normalizedProject = normalizeProject(project);
  if (!normalizedProject.children || normalizedProject.children.length === 0) {
    return 1;
  }

  return normalizedProject.children.reduce((total, child) => total + countLeafNodes(child), 0);
}

/**
 * Calculate the maximum depth of a project hierarchy
 */
function calculateMaxDepth(projects: Project[]): number {
  if (!projects || projects.length === 0) return 0;

  let maxDepth = 1;

  for (const project of projects) {
    const normalizedProject = normalizeProject(project);
    if (normalizedProject.children && normalizedProject.children.length > 0) {
      const childDepth = 1 + calculateMaxDepth(normalizedProject.children);
      maxDepth = Math.max(maxDepth, childDepth);
    }
  }

  return maxDepth;
}

/**
 * Calculate completion metrics for a project including all its descendants
 */
function calculateProjectCompletionMetrics(
  project: Project,
  allOutcomes: Outcome[] = []
): { totalOutcomes: number; completedOutcomes: number; completionPercentage: number } {
  // Get all project IDs in this project's tree (including itself)
  const projectIds = getAllProjectIds(project);

  // Filter outcomes for this project tree
  const projectOutcomes = allOutcomes.filter((outcome) => projectIds.includes(outcome.project));

  const totalOutcomes = projectOutcomes.length;
  const completedOutcomes = projectOutcomes.filter(isOutcomeCompleted).length;
  const completionPercentage = totalOutcomes > 0 ? Math.round((completedOutcomes / totalOutcomes) * 100) : 0;

  return {
    totalOutcomes,
    completedOutcomes,
    completionPercentage,
  };
}

/**
 * Get all project IDs in a project tree (including the project itself and all descendants)
 */
function getAllProjectIds(project: Project): string[] {
  const ids = [project.id];
  const normalizedProject = normalizeProject(project);

  if (normalizedProject.children && normalizedProject.children.length > 0) {
    for (const child of normalizedProject.children) {
      ids.push(...getAllProjectIds(child));
    }
  }

  return ids;
}

/**
 * Flatten a hierarchical project structure into table rows with rowSpan calculations
 */
function flattenProjectsToRows(
  projects: Project[],
  lifeAspectName: string,
  lifeAspectId: string,
  totalRowSpan: number,
  pathProjects: Project[] = [],
  maxDepth: number,
  isFirstRow: boolean = true,
  isFirstLevelProject: boolean = true,
  allOutcomes: Outcome[] = []
): TableRowData[] {
  const rows: TableRowData[] = [];

  projects.forEach((project, index) => {
    const normalizedProject = normalizeProject(project);
    const isLastInLevel = index === projects.length - 1;
    const hasChildren = normalizedProject.children && normalizedProject.children.length > 0;
    const leafNodeCount = countLeafNodes(normalizedProject);

    // Calculate outcomes for this project
    const outcomeStats = calculateProjectOutcomes(normalizedProject, allOutcomes);

    // Build project levels array with proper project data objects
    const currentPath = [...pathProjects, normalizedProject];
    const projectLevels = currentPath.map((pathProject, levelIndex) => {
      const pathProjectNormalized = normalizeProject(pathProject);
      const isCurrentProject = levelIndex === currentPath.length - 1;

      return {
        name: pathProjectNormalized.name,
        projectId: pathProjectNormalized.id,
        level: levelIndex,
        rowSpan: isCurrentProject ? leafNodeCount : 0, // Only the current project gets rowSpan
        shouldRender: isCurrentProject, // Only render the current project in this row
        // Enhanced project data for display
        priorityLevel: pathProjectNormalized.priority_level,
        priorityLevelName: pathProjectNormalized.priority_level_name,
        priorityLevelColor: pathProjectNormalized.priority_level_color,
        customFields: pathProjectNormalized.resolved_custom_fields || [],
        startDate: pathProjectNormalized.start_date,
        endDate: pathProjectNormalized.end_date,
        totalOutcomes: outcomeStats.totalOutcomes,
        completedOutcomes: outcomeStats.completedOutcomes,
        completionPercentage: outcomeStats.completionPercentage,
        parentProjectId: pathProjectNormalized.parent_id,
      };
    });

    // Create row data with properly mapped date fields
    const rowData: TableRowData = {
      id: normalizedProject.id,
      name: normalizedProject.name,
      description: normalizedProject.description,
      start_date: normalizedProject.start_date,
      end_date: normalizedProject.end_date,
      lifeAspectName,
      lifeAspectId,
      lifeAspectRowSpan: isFirstRow ? totalRowSpan : 0,
      resolved_custom_fields: normalizedProject.resolved_custom_fields || [],
      projectLevels,
      maxDepth,
      hasChildren,
      isExpanded: true,
      rowSpan: leafNodeCount,
      parent_id: normalizedProject.parent_id,
    };

    rows.push(rowData);

    if (hasChildren) {
      const childRows = flattenProjectsToRows(
        normalizedProject.children,
        lifeAspectName,
        lifeAspectId,
        totalRowSpan,
        currentPath,
        maxDepth,
        false,
        false,
        allOutcomes
      );
      rows.push(...childRows);
    }
  });

  return rows;
}

/**
 * Transform project hierarchy data into table-ready format with rowSpan calculations
 */
export function transformHierarchyToTableData(hierarchyData: ProjectHierarchy[], allOutcomes: Outcome[] = []): HierarchicalTableData {
  console.log("transformHierarchyToTableData called with:", hierarchyData);

  // Validate input
  if (!hierarchyData || !Array.isArray(hierarchyData) || hierarchyData.length === 0) {
    console.log("No valid hierarchy data provided, returning empty structure");
    return {
      rows: [],
      maxDepth: 0,
      columnHeaders: ["Life Aspect"],
    };
  }

  // Calculate overall maximum depth across all life aspects
  const depths = hierarchyData
    .filter((item) => item && item.projects) // Filter out invalid items
    .map((item) => calculateMaxDepth((item.projects || []).map(normalizeProject)));

  const globalMaxDepth = depths.length > 0 ? Math.max(...depths) : 0;

  // Generate column headers
  const columnHeaders = ["Life Aspect"];
  for (let i = 0; i < globalMaxDepth; i++) {
    columnHeaders.push(`Level ${i}`);
  }

  const allRows: TableRowData[] = [];

  // Process each life aspect
  for (const lifeAspectData of hierarchyData) {
    const { life_aspect, projects } = lifeAspectData;

    // Validate life_aspect data
    if (!life_aspect) {
      console.warn("Invalid life aspect data found:", lifeAspectData);
      continue;
    }

    // Ensure life_aspect has required fields
    const lifeAspectId = life_aspect.id || `temp-${Date.now()}-${Math.random()}`;
    const lifeAspectName = life_aspect.name || "Unknown Life Aspect";

    if (!projects || projects.length === 0) {
      // Life aspect with no projects
      allRows.push({
        id: `empty-${lifeAspectId}`,
        lifeAspectName: lifeAspectName,
        lifeAspectId: lifeAspectId,
        lifeAspectRowSpan: 1,
        projectLevels: [],
        maxDepth: globalMaxDepth,
      });
      continue;
    }

    // Normalize projects to ensure consistent structure
    const normalizedProjects = projects.map(normalizeProject);

    // Calculate total leaf nodes for this life aspect (for rowSpan)
    const totalLeafNodes = normalizedProjects.reduce((total, project) => total + countLeafNodes(project), 0);

    // Flatten projects to rows
    const lifeAspectRows = flattenProjectsToRows(
      normalizedProjects,
      lifeAspectName,
      lifeAspectId,
      totalLeafNodes,
      [],
      globalMaxDepth,
      true,
      true,
      allOutcomes
    );

    allRows.push(...lifeAspectRows);
  }

  return {
    rows: allRows,
    maxDepth: globalMaxDepth,
    columnHeaders,
  };
}

/**
 * Helper function to get the display value for a table cell
 */
export function getCellDisplayValue(row: TableRowData, columnIndex: number): { value: string; rowSpan: number; shouldRender: boolean } {
  if (columnIndex === 0) {
    // Life Aspect column
    return {
      value: row.lifeAspectName,
      rowSpan: row.lifeAspectRowSpan,
      shouldRender: row.lifeAspectRowSpan > 0,
    };
  }

  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex < row.projectLevels.length) {
    const projectLevel = row.projectLevels[projectLevelIndex];
    return {
      value: projectLevel.name || "",
      rowSpan: projectLevel.rowSpan || 1,
      shouldRender: projectLevel.shouldRender || false,
    };
  }

  // Empty cell
  return {
    value: "",
    rowSpan: 1,
    shouldRender: false,
  };
}

/**
 * Helper function to get project data for a table cell
 */
export function getCellProjectData(row: TableRowData, columnIndex: number): any | null {
  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex >= 0 && projectLevelIndex < row.projectLevels.length) {
    return row.projectLevels[projectLevelIndex];
  }

  return null;
}
